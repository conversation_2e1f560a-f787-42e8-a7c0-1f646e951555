{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header text-center bg-transparent">
                <h2 class="mb-0">
                    <i class="fas fa-palette me-2"></i>Генератор Зображень
                </h2>
                <p class="text-muted mt-2">Створюйте унікальні зображення за допомогою штучного інтелекту</p>
            </div>
            <div class="card-body p-4">
                <form id="generateForm">
                    <div class="mb-4">
                        <label for="prompt" class="form-label">
                            <i class="fas fa-pen-fancy me-2"></i>Опис зображення
                        </label>
                        <textarea 
                            class="form-control" 
                            id="prompt" 
                            rows="4" 
                            placeholder="Опишіть зображення, яке ви хочете створити... Наприклад: 'Красивий закат над океаном з пальмами на березі'"
                            required
                        ></textarea>
                        <div class="form-text">Будьте максимально детальними у вашому описі для кращого результату</div>
                    </div>

                    <div class="mb-4">
                        <label for="imageSize" class="form-label">
                            <i class="fas fa-expand-arrows-alt me-2"></i>Розмір зображення
                        </label>
                        <select class="form-select" id="imageSize">
                            <option value="512x512">512x512 (Квадрат, малий) - Швидко</option>
                            <option value="768x768">768x768 (Квадрат, середній) - Збалансовано</option>
                            <option value="1024x1024" selected>1024x1024 (Квадрат, великий) - Детально</option>
                            <option value="1152x896">1152x896 (Пейзаж) - Широкий вид</option>
                            <option value="896x1152">896x1152 (Портрет) - Вертикальний</option>
                            <option value="1344x768">1344x768 (Панорама) - Дуже широкий</option>
                            <option value="768x1344">768x1344 (Високий портрет) - Дуже високий</option>
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Вибраний розмір: <span id="selectedSize">1024x1024</span> |
                            Співвідношення: <span id="aspectRatio">1:1</span>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="generateBtn">
                            <i class="fas fa-magic me-2"></i>Згенерувати зображення
                        </button>
                    </div>
                </form>

                <!-- Loading indicator -->
                <div class="loading text-center mt-4" id="loadingDiv">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Генерація...</span>
                    </div>
                    <p class="mt-2">Генерую зображення... Це може зайняти кілька хвилин</p>
                </div>

                <!-- Results -->
                <div id="results" class="mt-4"></div>
            </div>
        </div>

        <!-- Приклади промптів -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Приклади промптів
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="example-prompt mb-3 p-3 bg-light rounded" style="cursor: pointer;">
                            <strong>Фантастичний пейзаж:</strong><br>
                            <small>"Магічний ліс з світними грибами, фіолетовим туманом та казковими істотами"</small>
                        </div>
                        <div class="example-prompt mb-3 p-3 bg-light rounded" style="cursor: pointer;">
                            <strong>Футуристичне місто:</strong><br>
                            <small>"Неонове кіберпанк місто вночі з літаючими автомобілями та хмарочосами"</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="example-prompt mb-3 p-3 bg-light rounded" style="cursor: pointer;">
                            <strong>Природа:</strong><br>
                            <small>"Спокійне гірське озеро на світанку з відображенням снігових вершин"</small>
                        </div>
                        <div class="example-prompt mb-3 p-3 bg-light rounded" style="cursor: pointer;">
                            <strong>Абстракція:</strong><br>
                            <small>"Абстрактна композиція з яскравими кольорами та геометричними формами"</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('generateForm');
    const loadingDiv = document.getElementById('loadingDiv');
    const resultsDiv = document.getElementById('results');
    const generateBtn = document.getElementById('generateBtn');
    const promptTextarea = document.getElementById('prompt');
    const imageSizeSelect = document.getElementById('imageSize');
    const selectedSizeSpan = document.getElementById('selectedSize');
    const aspectRatioSpan = document.getElementById('aspectRatio');

    // Функція для оновлення інформації про розмір
    function updateSizeInfo() {
        const size = imageSizeSelect.value;
        const [width, height] = size.split('x').map(Number);
        const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
        const divisor = gcd(width, height);
        const aspectW = width / divisor;
        const aspectH = height / divisor;

        selectedSizeSpan.textContent = size;
        aspectRatioSpan.textContent = `${aspectW}:${aspectH}`;
    }

    // Оновлюємо інформацію при зміні розміру
    imageSizeSelect.addEventListener('change', updateSizeInfo);
    updateSizeInfo(); // Початкове оновлення

    // Обробка кліків по прикладах промптів
    document.querySelectorAll('.example-prompt').forEach(example => {
        example.addEventListener('click', function() {
            const text = this.querySelector('small').textContent.replace(/"/g, '');
            promptTextarea.value = text;
        });
    });

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const prompt = document.getElementById('prompt').value;
        const size = document.getElementById('imageSize').value;
        
        if (!prompt.trim()) {
            alert('Будь ласка, введіть опис зображення');
            return;
        }

        // Показуємо індикатор завантаження
        loadingDiv.style.display = 'block';
        generateBtn.disabled = true;
        resultsDiv.innerHTML = '';

        try {
            const response = await fetch('/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt,
                    size: size
                })
            });

            const data = await response.json();

            if (data.success) {
                displayResults(data.images);
            } else {
                showError(data.error || 'Помилка при генерації зображення');
            }
        } catch (error) {
            showError('Помилка мережі: ' + error.message);
        } finally {
            loadingDiv.style.display = 'none';
            generateBtn.disabled = false;
        }
    });

    function displayResults(images) {
        const selectedSize = imageSizeSelect.value;
        let html = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                Зображення успішно згенеровано! Розмір: ${selectedSize}
            </div>
        `;

        images.forEach(image => {
            html += `
                <div class="text-center mb-3">
                    <div class="position-relative d-inline-block">
                        <img src="/image/${image.filename}" class="img-fluid generated-image" alt="Згенероване зображення">
                        <div class="position-absolute top-0 start-0 m-2">
                            <span class="badge bg-dark bg-opacity-75">${selectedSize}</span>
                        </div>
                    </div>
                    <div class="mt-2">
                        <a href="/image/${image.filename}" download class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download me-1"></i>Завантажити
                        </a>
                        <small class="text-muted d-block mt-1">Розмір: ${selectedSize} пікселів</small>
                    </div>
                </div>
            `;
        });

        resultsDiv.innerHTML = html;
    }

    function showError(message) {
        resultsDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
});
</script>
{% endblock %}
