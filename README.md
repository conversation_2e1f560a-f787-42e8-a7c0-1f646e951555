# AI Генератор Зображень

Веб-застосунок для генерації зображень за допомогою Google Gemini AI з можливістю вибору розміру зображення.

## Особливості

- 🎨 Генерація зображень за текстовим описом
- 📐 Вибір різних розмірів зображення (від 512x512 до 1344x768)
- 🖼️ Галерея згенерованих зображень
- 💾 Автоматичне збереження зображень
- 📱 Адаптивний дизайн
- 🌟 Сучасний інтерфейс з анімаціями

## Доступні розміри зображень

- 512x512 (<PERSON>в<PERSON><PERSON><PERSON><PERSON><PERSON>, малий)
- 768x768 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, середній)
- 1024x1024 (Квадрат, великий)
- 1152x896 (Пейзаж)
- 896x1152 (Портрет)
- 1344x768 (Широкий пейзаж)
- 768x1344 (Високий портрет)

## Встановлення

1. Клонуйте репозиторій або завантажте файли
2. Встановіть залежності:
```bash
pip install -r requirements.txt
```

3. Запустіть застосунок:
```bash
python app.py
```

4. Відкрийте браузер і перейдіть за адресою: http://localhost:5000

## Використання

1. **Головна сторінка**: Введіть опис зображення та виберіть бажаний розмір
2. **Генерація**: Натисніть кнопку "Згенерувати зображення" та зачекайте
3. **Галерея**: Переглядайте всі згенеровані зображення
4. **Завантаження**: Завантажуйте зображення на свій комп'ютер

## Приклади промптів

- "Магічний ліс з світними грибами, фіолетовим туманом та казковими істотами"
- "Неонове кіберпанк місто вночі з літаючими автомобілями та хмарочосами"
- "Спокійне гірське озеро на світанку з відображенням снігових вершин"
- "Абстрактна композиція з яскравими кольорами та геометричними формами"

## Структура проекту

```
├── app.py                 # Основний файл застосунку
├── requirements.txt       # Залежності Python
├── README.md             # Документація
├── templates/            # HTML шаблони
│   ├── base.html        # Базовий шаблон
│   ├── index.html       # Головна сторінка
│   └── gallery.html     # Галерея
└── generated_images/     # Папка для збережених зображень (створюється автоматично)
```

## API Endpoints

- `GET /` - Головна сторінка
- `POST /generate` - Генерація зображення
- `GET /image/<filename>` - Отримання зображення
- `GET /gallery` - Галерея зображень

## Технології

- **Backend**: Flask (Python)
- **Frontend**: Bootstrap 5, Font Awesome
- **AI**: Google Gemini 2.0 Flash Preview
- **Стилізація**: CSS3 з градієнтами та анімаціями

## Примітки

- Зображення зберігаються локально в папці `generated_images/`
- Кожне зображення має унікальне ім'я з часовою міткою
- Застосунок підтримує різні формати зображень (PNG, JPG, JPEG, GIF, WebP)
- Генерація може зайняти від кількох секунд до кількох хвилин залежно від складності промпту
