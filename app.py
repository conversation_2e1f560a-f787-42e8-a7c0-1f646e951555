import base64
import mimetypes
import os
import io
from flask import Flask, render_template, request, jsonify, send_file
from google import genai
from google.genai import types
import uuid
from datetime import datetime

app = Flask(__name__)

# Налаштування API ключа
GEMINI_API_KEY = "AIzaSyBi11VCTntbs_C15KpbcYbP1RLjEzEDnpM"

# Створюємо папку для збереження зображень
UPLOAD_FOLDER = 'generated_images'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def generate_image(prompt, image_size="1024x1024"):
    """Генерує зображення за допомогою Gemini API"""
    try:
        client = genai.Client(api_key=GEMINI_API_KEY)

        model = "gemini-2.0-flash-preview-image-generation"

        # Створюємо детальний промпт з урахуванням розміру
        size_instructions = {
            "512x512": "Create a square image with 512x512 pixel dimensions. The composition should be compact and well-balanced for a small square format.",
            "768x768": "Create a square image with 768x768 pixel dimensions. The composition should be detailed and well-balanced for a medium square format.",
            "1024x1024": "Create a square image with 1024x1024 pixel dimensions. The composition should be highly detailed and well-balanced for a large square format.",
            "1152x896": "Create a landscape-oriented image with 1152x896 pixel dimensions. The composition should emphasize horizontal elements and wide vistas.",
            "896x1152": "Create a portrait-oriented image with 896x1152 pixel dimensions. The composition should emphasize vertical elements and tall subjects.",
            "1344x768": "Create a wide landscape image with 1344x768 pixel dimensions. The composition should be panoramic with emphasis on horizontal breadth.",
            "768x1344": "Create a tall portrait image with 768x1344 pixel dimensions. The composition should emphasize height and vertical drama."
        }

        size_instruction = size_instructions.get(image_size, size_instructions["1024x1024"])

        # Створюємо повний промпт з детальними інструкціями
        full_prompt = f"""
{prompt}

Technical requirements:
- {size_instruction}
- High quality, detailed artwork
- Professional composition
- Aspect ratio: {image_size.replace('x', ':')}
- Resolution: {image_size} pixels

Please generate an image that strictly follows these dimensional requirements.
"""

        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=full_prompt),
                ],
            ),
        ]

        generate_content_config = types.GenerateContentConfig(
            top_p=0.8,  # Зменшуємо для більш стабільних результатів
            response_modalities=[
                "IMAGE",
                "TEXT",
            ],
        )

        generated_files = []
        file_index = 0
        
        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue
                
            if chunk.candidates[0].content.parts[0].inline_data and chunk.candidates[0].content.parts[0].inline_data.data:
                # Генеруємо унікальне ім'я файлу з інформацією про розмір
                unique_id = str(uuid.uuid4())[:8]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                size_tag = image_size.replace('x', '_')
                file_name = f"generated_{timestamp}_{size_tag}_{unique_id}_{file_index}"
                file_index += 1

                inline_data = chunk.candidates[0].content.parts[0].inline_data
                data_buffer = inline_data.data
                file_extension = mimetypes.guess_extension(inline_data.mime_type) or '.png'

                full_file_path = os.path.join(UPLOAD_FOLDER, f"{file_name}{file_extension}")

                # Зберігаємо файл
                with open(full_file_path, "wb") as f:
                    f.write(data_buffer)

                generated_files.append({
                    'filename': f"{file_name}{file_extension}",
                    'path': full_file_path,
                    'size': image_size
                })
        
        return generated_files, None
        
    except Exception as e:
        return None, str(e)

@app.route('/')
def index():
    """Головна сторінка"""
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate():
    """API endpoint для генерації зображення"""
    try:
        data = request.get_json()
        prompt = data.get('prompt', '')
        image_size = data.get('size', '1024x1024')
        
        if not prompt:
            return jsonify({'error': 'Prompt is required'}), 400
        
        generated_files, error = generate_image(prompt, image_size)
        
        if error:
            return jsonify({'error': error}), 500
        
        if not generated_files:
            return jsonify({'error': 'No images were generated'}), 500
        
        # Повертаємо інформацію про згенеровані файли
        return jsonify({
            'success': True,
            'images': [{'filename': file['filename']} for file in generated_files]
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/image/<filename>')
def get_image(filename):
    """Повертає згенероване зображення"""
    try:
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            return jsonify({'error': 'Image not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/gallery')
def gallery():
    """Сторінка галереї згенерованих зображень"""
    try:
        images = []
        if os.path.exists(UPLOAD_FOLDER):
            for filename in os.listdir(UPLOAD_FOLDER):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                    images.append(filename)
        
        # Сортуємо за датою створення (новіші спочатку)
        images.sort(reverse=True)
        
        return render_template('gallery.html', images=images)
    except Exception as e:
        return render_template('gallery.html', images=[], error=str(e))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
