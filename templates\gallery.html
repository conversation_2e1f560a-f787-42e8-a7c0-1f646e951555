{% extends "base.html" %}

{% block title %}Галерея - AI Генератор Зображень{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header text-center bg-transparent">
                <h2 class="mb-0">
                    <i class="fas fa-images me-2"></i>Галерея Згенерованих Зображень
                </h2>
                <p class="text-muted mt-2">Перегляньте всі ваші створені зображення</p>
            </div>
            <div class="card-body">
                {% if error %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Помилка: {{ error }}
                    </div>
                {% elif not images %}
                    <div class="text-center py-5">
                        <i class="fas fa-image fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">Поки що немає згенерованих зображень</h4>
                        <p class="text-muted">Перейдіть на головну сторінку, щоб створити ваше перше зображення</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Створити зображення
                        </a>
                    </div>
                {% else %}
                    <div class="row">
                        {% for image in images %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="position-relative">
                                    <img src="{{ url_for('get_image', filename=image) }}" 
                                         class="card-img-top generated-image" 
                                         alt="Згенероване зображення"
                                         style="height: 250px; object-fit: cover; cursor: pointer;"
                                         onclick="openImageModal('{{ url_for('get_image', filename=image) }}', '{{ image }}')">
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <button class="btn btn-sm btn-light rounded-circle" 
                                                onclick="downloadImage('{{ url_for('get_image', filename=image) }}', '{{ image }}')"
                                                title="Завантажити">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title text-truncate">{{ image }}</h6>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            {% set parts = image.split('_') %}
                                            {% if parts|length >= 2 %}
                                                {{ parts[1][:8] }} {{ parts[1][9:11] }}:{{ parts[1][11:13] }}:{{ parts[1][13:15] }}
                                            {% endif %}
                                        </small>
                                        {% set parts = image.split('_') %}
                                        {% if parts|length >= 4 %}
                                            <span class="badge bg-primary">{{ parts[2].replace('_', 'x') }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex justify-content-between">
                                        <button class="btn btn-outline-primary btn-sm" 
                                                onclick="openImageModal('{{ url_for('get_image', filename=image) }}', '{{ image }}')">
                                            <i class="fas fa-eye me-1"></i>Переглянути
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="downloadImage('{{ url_for('get_image', filename=image) }}', '{{ image }}')">
                                            <i class="fas fa-download me-1"></i>Завантажити
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    {% if images|length > 0 %}
                    <div class="text-center mt-4">
                        <p class="text-muted">Всього зображень: {{ images|length }}</p>
                    </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal для перегляду зображення -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Перегляд зображення</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Закрити"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="Зображення">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрити</button>
                <button type="button" class="btn btn-primary" id="modalDownloadBtn">
                    <i class="fas fa-download me-2"></i>Завантажити
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentImageUrl = '';
let currentImageName = '';

function openImageModal(imageUrl, imageName) {
    currentImageUrl = imageUrl;
    currentImageName = imageName;
    
    document.getElementById('modalImage').src = imageUrl;
    document.getElementById('imageModalLabel').textContent = imageName;
    
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    modal.show();
}

function downloadImage(imageUrl, imageName) {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = imageName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Обробник для кнопки завантаження в модальному вікні
document.getElementById('modalDownloadBtn').addEventListener('click', function() {
    if (currentImageUrl && currentImageName) {
        downloadImage(currentImageUrl, currentImageName);
    }
});

// Додаємо можливість закрити модальне вікно клавішею Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modal = bootstrap.Modal.getInstance(document.getElementById('imageModal'));
        if (modal) {
            modal.hide();
        }
    }
});
</script>
{% endblock %}
